package com.oriondev.luatdansu;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

public class ChapterAdapter extends RecyclerView.Adapter<ChapterAdapter.ChapterViewHolder> {

    private List<Chapter> chapters = new ArrayList<>();
    private OnChapterClickListener listener;

    public interface OnChapterClickListener {
        void onChapterClick(Chapter chapter);
    }

    public ChapterAdapter(OnChapterClickListener listener) {
        this.listener = listener;
    }

    public void setChapters(List<Chapter> chapters) {
        this.chapters = chapters;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ChapterViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_chapter, parent, false);
        return new ChapterViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ChapterViewHolder holder, int position) {
        Chapter chapter = chapters.get(position);
        holder.bind(chapter);
    }

    @Override
    public int getItemCount() {
        return chapters.size();
    }

    class ChapterViewHolder extends RecyclerView.ViewHolder {
        private TextView titleText;
        private TextView descriptionText;

        public ChapterViewHolder(@NonNull View itemView) {
            super(itemView);
            titleText = itemView.findViewById(R.id.chapter_title);
            descriptionText = itemView.findViewById(R.id.description_text);
        }

        public void bind(Chapter chapter) {
            // Chỉ hiển thị tên chương với format " Tên chương"
            titleText.setText(" " + chapter.getTitle().toUpperCase());
            
            // Ẩn description để không bị trùng lặp
            descriptionText.setVisibility(View.GONE);
            
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onChapterClick(chapter);
                }
            });
        }
    }
}
