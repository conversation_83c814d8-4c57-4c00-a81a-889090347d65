package com.oriondev.luatdansu;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.ListView;
import android.widget.Toast;
import android.widget.ArrayAdapter;

import com.oriondev.luatdansu.utils.JsonDataLoader;
import com.oriondev.luatdansu.database.entity.LawEntity;

import java.util.ArrayList;
import java.util.List;

public class ChaptersActivityNew extends Activity {

    private static final String TAG = "ChaptersActivity";
    
    private ListView listView;
    private ArrayAdapter<String> adapter;
    private List<String> chapterTitles;
    private List<String> chapterNumbers;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_chapters);

        initViews();
        loadChapters();
    }

    private void initViews() {
        listView = findViewById(R.id.recycler_view);
        chapterTitles = new ArrayList<>();
        chapterNumbers = new ArrayList<>();
        
        adapter = new ArrayAdapter<>(this, android.R.layout.simple_list_item_1, chapterTitles);
        listView.setAdapter(adapter);
        
        listView.setOnItemClickListener((parent, view, position, id) -> {
            String chapterNumber = chapterNumbers.get(position);
            String chapterTitle = chapterTitles.get(position);
            
            Intent intent = new Intent(this, MainActivity.class);
            intent.putExtra("chapter_number", chapterNumber);
            intent.putExtra("chapter_title", chapterTitle);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
        });
    }

    private void loadChapters() {
        new Thread(() -> {
            try {
                JsonDataLoader loader = new JsonDataLoader(this);
                List<LawEntity> lawEntities = loader.loadLawDataFromAssets();
                
                List<String> titles = new ArrayList<>();
                List<String> numbers = new ArrayList<>();
                
                for (LawEntity entity : lawEntities) {
                    if (entity.isChapter()) {
                        titles.add(" " + entity.getTitle().toUpperCase());
                        numbers.add(entity.getNumber());
                    }
                }

                runOnUiThread(() -> {
                    chapterTitles.clear();
                    chapterTitles.addAll(titles);
                    chapterNumbers.clear();
                    chapterNumbers.addAll(numbers);
                    adapter.notifyDataSetChanged();
                    Toast.makeText(this, "Đã tải " + titles.size() + " chương", Toast.LENGTH_SHORT).show();
                });

            } catch (Exception e) {
                Log.e(TAG, "Error loading chapters", e);
                runOnUiThread(() -> 
                    Toast.makeText(this, "Lỗi tải dữ liệu: " + e.getMessage(), Toast.LENGTH_SHORT).show()
                );
            }
        }).start();
    }
}
