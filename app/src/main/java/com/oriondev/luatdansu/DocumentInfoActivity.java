package com.oriondev.luatdansu;

import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;

import com.google.android.material.navigation.NavigationView;
import com.oriondev.luatdansu.utils.JsonDataLoader;
import com.oriondev.luatdansu.database.entity.LawEntity;

import java.util.List;

public class DocumentInfoActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    private static final String TAG = "DocumentInfoActivity";

    // Advanced UI Components with comprehensive functionality
    private Toolbar toolbar;
    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private ActionBarDrawerToggle drawerToggle;
    private LinearLayout contentContainer;

    // Document information display components
    private TextView documentNameText, documentSummaryText, chapterCountText;
    private TextView articleCountText, issuingAuthorityText, issueDateText;
    private TextView effectiveDateText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_chapters);

        try {
            Log.d(TAG, "Starting comprehensive DocumentInfoActivity initialization");

            // Initialize advanced view components
            initializeAdvancedViewComponents();

            // Setup comprehensive toolbar and navigation
            setupAdvancedToolbarAndNavigation();

            // Create comprehensive document info layout
            setupAdvancedDocumentInfoLayout();

            // Load and process comprehensive document data
            loadAdvancedDocumentData();

            Log.d(TAG, "DocumentInfoActivity initialization completed successfully");

        } catch (Exception e) {
            Log.e(TAG, "Critical error during DocumentInfoActivity initialization", e);
            handleAdvancedInitializationError(e);
        }
    }

    /**
     * Initialize advanced view components with comprehensive functionality
     */
    private void initializeAdvancedViewComponents() {
        try {
            // Core navigation components
            toolbar = findViewById(R.id.toolbar);
            drawerLayout = findViewById(R.id.drawer_layout);
            navigationView = findViewById(R.id.navigation_view);

            // Create comprehensive dynamic content container
            contentContainer = new LinearLayout(this);
            contentContainer.setOrientation(LinearLayout.VERTICAL);
            contentContainer.setPadding(24, 24, 24, 24);
            contentContainer.setBackgroundColor(Color.WHITE);

            // Find main content area and replace with our comprehensive container
            View mainContent = findViewById(R.id.recyclerView);
            if (mainContent != null && mainContent.getParent() instanceof LinearLayout) {
                LinearLayout parent = (LinearLayout) mainContent.getParent();
                parent.removeView(mainContent);
                parent.addView(contentContainer, 0);
            } else {
                // Fallback: create new layout and set as content
                createFallbackContentContainer();
                return;
            }

            Log.d(TAG, "Advanced view components initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing view components", e);
            // Don't throw exception, continue with fallback
            createFallbackContentContainer();
        }
    }

    /**
     * Create fallback content container if main initialization fails
     */
    private void createFallbackContentContainer() {
        try {
            // Create scroll view for better content display
            ScrollView scrollView = new ScrollView(this);
            scrollView.setFillViewport(true);

            contentContainer = new LinearLayout(this);
            contentContainer.setOrientation(LinearLayout.VERTICAL);
            contentContainer.setPadding(24, 24, 24, 24);
            contentContainer.setBackgroundColor(Color.WHITE);

            scrollView.addView(contentContainer);

            // Set as main content
            setContentView(scrollView);

            Log.d(TAG, "Fallback content container created");
        } catch (Exception e) {
            Log.e(TAG, "Failed to create fallback container", e);
        }
    }

    /**
     * Setup advanced toolbar and navigation with comprehensive features
     */
    private void setupAdvancedToolbarAndNavigation() {
        try {
            // Setup toolbar with advanced configuration
            if (toolbar != null) {
                setSupportActionBar(toolbar);
                if (getSupportActionBar() != null) {
                    getSupportActionBar().setTitle("Thông tin văn bản");
                    getSupportActionBar().setDisplayHomeAsUpEnabled(true);
                }
            }

            // Setup advanced navigation drawer
            if (drawerLayout != null && toolbar != null) {
                drawerToggle = new ActionBarDrawerToggle(
                    this, drawerLayout, toolbar,
                    R.string.navigation_drawer_open,
                    R.string.navigation_drawer_close
                );
                drawerLayout.addDrawerListener(drawerToggle);
                drawerToggle.syncState();
            }

            // Setup navigation view with comprehensive listener
            if (navigationView != null) {
                navigationView.setNavigationItemSelectedListener(this);
            }

            Log.d(TAG, "Advanced toolbar and navigation setup completed");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up toolbar and navigation", e);
        }
    }

    /**
     * Setup comprehensive document info layout with advanced features
     */
    private void setupAdvancedDocumentInfoLayout() {
        try {
            if (contentContainer != null) {
                // Create comprehensive document cards
                createAdvancedDocumentCard();
                createAdvancedStatisticsCard();
                createAdvancedMetadataCard();
            }

            Log.d(TAG, "Advanced document info layout setup completed");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up document info layout", e);
        }
    }

    /**
     * Create advanced document card with comprehensive information
     */
    private void createAdvancedDocumentCard() {
        try {
            CardView documentCard = new CardView(this);
            documentCard.setCardElevation(8f);
            documentCard.setRadius(16f);
            documentCard.setUseCompatPadding(true);

            LinearLayout cardContent = new LinearLayout(this);
            cardContent.setOrientation(LinearLayout.VERTICAL);
            cardContent.setPadding(24, 24, 24, 24);

            // Document title
            TextView titleText = createAdvancedStyledTextView("📋 THÔNG TIN VĂN BẢN", 20, true, Color.parseColor("#1976D2"));
            cardContent.addView(titleText);

            // Document name
            documentNameText = createAdvancedStyledTextView("Bộ Luật Dân sự Việt Nam", 18, true, Color.BLACK);
            cardContent.addView(documentNameText);

            // Document summary
            documentSummaryText = createAdvancedStyledTextView("Bộ luật quy định về các quan hệ dân sự, bao gồm quan hệ tài sản và quan hệ nhân thân giữa các chủ thể bình đẳng.", 14, false, Color.GRAY);
            cardContent.addView(documentSummaryText);

            documentCard.addView(cardContent);
            contentContainer.addView(documentCard);

        } catch (Exception e) {
            Log.e(TAG, "Error creating document card", e);
        }
    }

    /**
     * Create advanced statistics card with comprehensive analytics
     */
    private void createAdvancedStatisticsCard() {
        try {
            CardView statisticsCard = new CardView(this);
            statisticsCard.setCardElevation(8f);
            statisticsCard.setRadius(16f);
            statisticsCard.setUseCompatPadding(true);

            LinearLayout cardContent = new LinearLayout(this);
            cardContent.setOrientation(LinearLayout.VERTICAL);
            cardContent.setPadding(24, 24, 24, 24);

            // Statistics title
            TextView titleText = createAdvancedStyledTextView("📊 THỐNG KÊ NỘI DUNG", 18, true, Color.parseColor("#388E3C"));
            cardContent.addView(titleText);

            // Chapter count
            chapterCountText = createAdvancedStyledTextView("📚 Số chương: 24", 16, false, Color.BLACK);
            cardContent.addView(chapterCountText);

            // Article count
            articleCountText = createAdvancedStyledTextView("📝 Số điều: 689", 16, false, Color.BLACK);
            cardContent.addView(articleCountText);

            statisticsCard.addView(cardContent);
            contentContainer.addView(statisticsCard);

        } catch (Exception e) {
            Log.e(TAG, "Error creating statistics card", e);
        }
    }

    /**
     * Create advanced metadata card with comprehensive legal information
     */
    private void createAdvancedMetadataCard() {
        try {
            CardView metadataCard = new CardView(this);
            metadataCard.setCardElevation(8f);
            metadataCard.setRadius(16f);
            metadataCard.setUseCompatPadding(true);

            LinearLayout cardContent = new LinearLayout(this);
            cardContent.setOrientation(LinearLayout.VERTICAL);
            cardContent.setPadding(24, 24, 24, 24);

            // Metadata title
            TextView titleText = createAdvancedStyledTextView("ℹ️ THÔNG TIN PHÁT HÀNH", 18, true, Color.parseColor("#F57C00"));
            cardContent.addView(titleText);

            // Issuing authority
            issuingAuthorityText = createAdvancedStyledTextView("🏛️ Cơ quan ban hành: Quốc hội Việt Nam", 16, false, Color.BLACK);
            cardContent.addView(issuingAuthorityText);

            // Issue date
            issueDateText = createAdvancedStyledTextView("📅 Ngày ban hành: 24/11/2015", 16, false, Color.BLACK);
            cardContent.addView(issueDateText);

            // Effective date
            effectiveDateText = createAdvancedStyledTextView("⚡ Ngày hiệu lực: 01/01/2017", 16, false, Color.BLACK);
            cardContent.addView(effectiveDateText);

            metadataCard.addView(cardContent);
            contentContainer.addView(metadataCard);

        } catch (Exception e) {
            Log.e(TAG, "Error creating metadata card", e);
        }
    }

    /**
     * Create advanced styled TextView with comprehensive formatting
     */
    private TextView createAdvancedStyledTextView(String text, int textSize, boolean isBold, int textColor) {
        TextView textView = new TextView(this);
        textView.setText(text);
        textView.setTextSize(textSize);
        textView.setTextColor(textColor);

        if (isBold) {
            textView.setTypeface(textView.getTypeface(), Typeface.BOLD);
        }

        // Add margins for better spacing
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(0, 8, 0, 8);
        textView.setLayoutParams(params);

        return textView;
    }

    /**
     * Load and process comprehensive document data
     */
    private void loadAdvancedDocumentData() {
        try {
            // Load law data from JSON immediately on main thread for faster display
            JsonDataLoader loader = new JsonDataLoader(this);
            List<LawEntity> lawEntities = loader.loadLawDataFromAssets();

            if (lawEntities != null && !lawEntities.isEmpty()) {
                // Update UI with real data immediately
                updateAdvancedDocumentInfoUI(lawEntities);
                Log.d(TAG, "Document data loaded successfully: " + lawEntities.size() + " entities");
            } else {
                Log.w(TAG, "No law entities found in JSON data");
                // Show default data
                showDefaultDocumentInfo();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in loadAdvancedDocumentData", e);
            // Show default data on error
            showDefaultDocumentInfo();
        }
    }

    /**
     * Show default document information when JSON loading fails
     */
    private void showDefaultDocumentInfo() {
        try {
            // Update with default values
            if (chapterCountText != null) {
                chapterCountText.setText("📚 Số chương: 24");
            }
            if (articleCountText != null) {
                articleCountText.setText("📝 Số điều: 689");
            }
            Log.d(TAG, "Default document info displayed");
        } catch (Exception e) {
            Log.e(TAG, "Error showing default document info", e);
        }
    }

    /**
     * Update UI with comprehensive document information
     */
    private void updateAdvancedDocumentInfoUI(List<LawEntity> lawEntities) {
        try {
            int chapterCount = 0;
            int articleCount = 0;

            for (LawEntity entity : lawEntities) {
                if (entity.isChapter()) {
                    chapterCount++;
                } else if (entity.isArticle()) {
                    articleCount++;
                }
            }

            // Update statistics
            if (chapterCountText != null) {
                chapterCountText.setText("📚 Số chương: " + chapterCount);
            }
            if (articleCountText != null) {
                articleCountText.setText("📝 Số điều: " + articleCount);
            }

            Log.d(TAG, "Document info UI updated successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error updating document info UI", e);
        }
    }

    /**
     * Handle advanced initialization error
     */
    private void handleAdvancedInitializationError(Exception e) {
        Log.e(TAG, "Initialization error", e);
        Toast.makeText(this, "Lỗi khởi tạo: " + e.getMessage(), Toast.LENGTH_LONG).show();
        finish();
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.nav_home) {
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
        } else if (id == R.id.nav_chapters) {
            Intent intent = new Intent(this, ChaptersActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_bookmarks) {
            Intent intent = new Intent(this, BookmarkActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_document_info) {
            // Already in this activity
            Toast.makeText(this, "Bạn đang ở trang Thông tin văn bản", Toast.LENGTH_SHORT).show();
        } else if (id == R.id.nav_share) {
            handleShareAction();
        } else if (id == R.id.nav_privacy) {
            handlePrivacyPolicyAction();
        }

        if (drawerLayout != null) {
            drawerLayout.closeDrawer(GravityCompat.START);
        }
        return true;
    }

    private void handleShareAction() {
        try {
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("text/plain");
            shareIntent.putExtra(Intent.EXTRA_SUBJECT, "Luật Dân sự Việt Nam");
            shareIntent.putExtra(Intent.EXTRA_TEXT,
                "Tải ứng dụng Luật Dân sự Việt Nam để tra cứu pháp luật dễ dàng!\n" +
                "https://play.google.com/store/apps/details?id=com.oriondev.luatdansu");

            Intent chooser = Intent.createChooser(shareIntent, "Chia sẻ ứng dụng qua");
            startActivity(chooser);
        } catch (Exception e) {
            Toast.makeText(this, "Không thể chia sẻ ứng dụng", Toast.LENGTH_SHORT).show();
        }
    }

    private void handlePrivacyPolicyAction() {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse("https://sites.google.com/view/csbmi/home"));
            startActivity(intent);
        } catch (Exception e) {
            Toast.makeText(this, "Không thể mở chính sách bảo mật", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onBackPressed() {
        if (drawerLayout != null && drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else {
            super.onBackPressed();
        }
    }
}
