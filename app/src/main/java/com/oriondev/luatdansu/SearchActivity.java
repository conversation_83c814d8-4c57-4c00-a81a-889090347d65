package com.oriondev.luatdansu;

import android.app.SearchManager;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SearchView;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.progressindicator.LinearProgressIndicator;

import com.oriondev.luatdansu.adapter.LawContentAdapter;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.viewmodel.SearchViewModel;

public class SearchActivity extends AppCompatActivity {

    private EditText searchEditText;
    private ImageButton searchClear;
    private RecyclerView recyclerView;
    private TextView emptyStateText;
    private LinearProgressIndicator progressBar;
    private MaterialToolbar toolbar;

    private SearchViewModel viewModel;
    private LawContentAdapter adapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_search);

        initializeViews();
        setupToolbar();
        setupRecyclerView();
        setupViewModel();
        setupObservers();
        setupSearchView();

        // Handle intent if launched with search query
        handleSearchIntent(getIntent());
    }

    private void initializeViews() {
        toolbar = findViewById(R.id.toolbar);
        searchEditText = findViewById(R.id.search_edit_text);
        searchClear = findViewById(R.id.search_clear);
        recyclerView = findViewById(R.id.recycler_view);
        emptyStateText = findViewById(R.id.empty_state_text);
        progressBar = findViewById(R.id.progress_bar);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Tìm kiếm");
        }
    }

    private void setupRecyclerView() {
        adapter = new LawContentAdapter(this, new LawContentAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(LawEntity lawItem) {
                // Return result to MainActivity
                Intent resultIntent = new Intent();
                resultIntent.putExtra("selected_law_item_id", lawItem.getId());
                resultIntent.putExtra("chapter_number", lawItem.getChapterNumber());
                resultIntent.putExtra("article_number", lawItem.getArticleNumber());
                setResult(RESULT_OK, resultIntent);
                finish();
            }

            @Override
            public void onItemLongClick(LawEntity lawItem) {
                // Handle bookmark or context menu
                viewModel.addToSearchHistory(searchEditText.getText().toString(), 1);
            }

            @Override
            public void onBookmarkClick(LawEntity lawItem) {
                // Handle bookmark toggle
                if (viewModel != null) {
                    viewModel.toggleBookmark(lawItem);
                    Toast.makeText(SearchActivity.this, "Đã thêm vào bookmark", Toast.LENGTH_SHORT).show();
                }
            }
        });

        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void setupViewModel() {
        viewModel = new ViewModelProvider(this).get(SearchViewModel.class);
    }

    private void setupObservers() {
        // Observe search results
        viewModel.getSearchResults().observe(this, results -> {
            if (results != null && !results.isEmpty()) {
                adapter.setLawItems(results);
                recyclerView.setVisibility(View.VISIBLE);
                emptyStateText.setVisibility(View.GONE);

                // Add to search history
                String query = viewModel.getCurrentQueryValue();
                if (query != null) {
                    viewModel.addToSearchHistory(query, results.size());
                }
            } else {
                recyclerView.setVisibility(View.GONE);
                emptyStateText.setVisibility(View.VISIBLE);
                emptyStateText.setText("Không tìm thấy kết quả nào");
            }
        });

        // Observe loading state - DISABLED
        viewModel.isSearching().observe(this, isSearching -> {
            // Loading indicator disabled per user request
            progressBar.setVisibility(View.GONE);
        });

        // Observe error messages
        viewModel.getErrorMessage().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                emptyStateText.setVisibility(View.VISIBLE);
                emptyStateText.setText(error);
                recyclerView.setVisibility(View.GONE);
                viewModel.clearError();
            }
        });
    }

    private void setupSearchView() {
        searchEditText.requestFocus();

        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String query = s.toString().trim();

                if (query.isEmpty()) {
                    searchClear.setVisibility(View.GONE);
                    viewModel.clearSearch();
                    recyclerView.setVisibility(View.GONE);
                    emptyStateText.setVisibility(View.VISIBLE);
                    emptyStateText.setText("Nhập từ khóa để tìm kiếm");
                } else {
                    searchClear.setVisibility(View.VISIBLE);
                    performSearch(query);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        // Clear button click
        searchClear.setOnClickListener(v -> {
            searchEditText.setText("");
            viewModel.clearSearch();
            recyclerView.setVisibility(View.GONE);
            emptyStateText.setVisibility(View.VISIBLE);
            emptyStateText.setText("Nhập từ khóa để tìm kiếm");
        });

        // Handle keyboard search action
        searchEditText.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                String query = searchEditText.getText().toString().trim();
                if (!query.isEmpty()) {
                    performSearch(query);
                }
                return true;
            }
            return false;
        });
    }

    private void performSearch(String query) {
        viewModel.search(query);
        adapter.setSearchQuery(query);
    }

    private void handleSearchIntent(Intent intent) {
        if (Intent.ACTION_SEARCH.equals(intent.getAction())) {
            String query = intent.getStringExtra(SearchManager.QUERY);
            if (query != null && !query.isEmpty()) {
                searchEditText.setText(query);
                performSearch(query);
            }
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        handleSearchIntent(intent);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finish();
    }
}
