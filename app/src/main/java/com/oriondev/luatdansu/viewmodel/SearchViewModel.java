package com.oriondev.luatdansu.viewmodel;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.oriondev.luatdansu.database.entity.BookmarkEntity;
import com.oriondev.luatdansu.database.entity.LawEntity;
import com.oriondev.luatdansu.database.entity.SearchHistoryEntity;
import com.oriondev.luatdansu.repository.LawRepository;

import java.util.List;

public class SearchViewModel extends AndroidViewModel {
    private LawRepository repository;
    
    private MutableLiveData<String> searchQuery = new MutableLiveData<>();
    private MutableLiveData<Boolean> isSearching = new MutableLiveData<>(false);
    private MutableLiveData<String> errorMessage = new MutableLiveData<>();
    
    private LiveData<List<LawEntity>> searchResults;
    private LiveData<List<SearchHistoryEntity>> searchHistory;
    private LiveData<List<String>> searchSuggestions;

    public SearchViewModel(@NonNull Application application) {
        super(application);
        repository = new LawRepository(application);
        
        searchHistory = repository.getRecentSearchHistory();
        searchSuggestions = repository.getTopQueries(10);
        
        // Setup search results transformation
        searchResults = Transformations.switchMap(searchQuery, query -> {
            if (query != null && !query.trim().isEmpty()) {
                isSearching.setValue(true);
                return repository.searchLawItems(query.trim());
            } else {
                isSearching.setValue(false);
                return new MutableLiveData<>(null);
            }
        });
    }

    // Getters for LiveData
    public LiveData<List<LawEntity>> getSearchResults() {
        return searchResults;
    }

    public LiveData<List<SearchHistoryEntity>> getSearchHistory() {
        return searchHistory;
    }

    public LiveData<List<String>> getSearchSuggestions() {
        return searchSuggestions;
    }

    public LiveData<Boolean> isSearching() {
        return isSearching;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<String> getCurrentQuery() {
        return searchQuery;
    }

    // Actions
    public void search(String query) {
        if (query == null || query.trim().isEmpty()) {
            searchQuery.setValue(null);
            return;
        }
        
        String trimmedQuery = query.trim();
        searchQuery.setValue(trimmedQuery);
        
        // Add to search history after getting results
        // We'll observe the results and add to history when they're available
    }

    public void addToSearchHistory(String query, int resultCount) {
        if (query != null && !query.trim().isEmpty()) {
            repository.insertSearchHistory(query.trim(), resultCount);
        }
    }

    public void clearSearch() {
        searchQuery.setValue(null);
        isSearching.setValue(false);
        clearError();
    }

    public void deleteSearchHistory(SearchHistoryEntity searchHistory) {
        repository.deleteSearchHistory(searchHistory);
    }

    public void clearAllSearchHistory() {
        repository.deleteAllSearchHistory();
    }

    public void toggleBookmark(LawEntity lawItem) {
        if (lawItem == null) return;

        // Create bookmark
        String contentPreview = lawItem.getContent();
        if (contentPreview != null && contentPreview.length() > 100) {
            contentPreview = contentPreview.substring(0, 100) + "...";
        }

        BookmarkEntity bookmark = new BookmarkEntity(
            lawItem.getId(),
            lawItem.getDisplayTitle(),
            contentPreview,
            lawItem.getChapterNumber(),
            lawItem.getArticleNumber(),
            null // No note initially
        );
        repository.insertBookmark(bookmark);
    }

    public void setError(String error) {
        errorMessage.setValue(error);
        isSearching.setValue(false);
    }

    public void clearError() {
        errorMessage.setValue(null);
    }

    // Helper methods
    public boolean hasQuery() {
        String query = searchQuery.getValue();
        return query != null && !query.trim().isEmpty();
    }

    public String getCurrentQueryValue() {
        return searchQuery.getValue();
    }

    // Search in specific chapter
    public LiveData<List<LawEntity>> searchInChapter(String chapterNumber, String query) {
        if (query != null && !query.trim().isEmpty() && chapterNumber != null) {
            return repository.searchLawItems(query.trim()); // Can be enhanced to search in specific chapter
        }
        return new MutableLiveData<>(null);
    }

    // Advanced search methods (can be implemented later)
    public void searchByType(String type, String query) {
        // Implementation for searching by specific type (chapter, article, clause)
    }

    public void searchWithFilters(String query, String chapterFilter, String articleFilter) {
        // Implementation for filtered search
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        repository.cleanup();
    }
}
