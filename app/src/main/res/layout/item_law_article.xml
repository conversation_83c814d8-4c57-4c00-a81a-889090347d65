<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:clickable="true"
    android:focusable="true"
    android:background="?android:attr/selectableItemBackground">

    <!-- Chapter Title (chỉ hiển thị cho chapters) -->
    <TextView
        android:id="@+id/chapter_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/primary_color"
        android:paddingTop="20dp"
        android:paddingBottom="8dp"
        android:gravity="center"
        android:text="Chương II"
        android:visibility="gone" />

    <!-- Chapter Subtitle -->
    <TextView
        android:id="@+id/chapter_subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:paddingBottom="16dp"
        android:gravity="center"
        android:text="CÁ NHÂN"
        android:visibility="gone" />

    <!-- Article Number and Title (hiển thị trên cùng một dòng) -->
    <LinearLayout
        android:id="@+id/article_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="12dp"
        android:paddingBottom="8dp"
        android:visibility="gone">

        <TextView
            android:id="@+id/number_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:text="Điều 1."
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/title_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:text="Phạm vi điều chỉnh"
            android:lineSpacingExtra="2dp" />

        <ImageButton
            android:id="@+id/bookmark_button"
            style="@style/Widget.Material3.Button.IconButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_bookmark_border"
            android:contentDescription="Bookmark"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Article Content -->
    <TextView
        android:id="@+id/content_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="15sp"
        android:textColor="@color/text_secondary"
        android:text="Bộ luật này quy định địa vị pháp lý, chuẩn mực pháp lý về cách ứng xử của cá nhân, pháp nhân..."
        android:lineSpacingExtra="6dp"
        android:justificationMode="inter_word"
        android:paddingBottom="16dp"
        android:visibility="gone" />

    <!-- Thin divider -->
    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E8E8E8"
        android:layout_marginTop="4dp"
        android:visibility="gone" />

</LinearLayout>